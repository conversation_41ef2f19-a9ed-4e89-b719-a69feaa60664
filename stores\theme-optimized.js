import { defineStore } from 'pinia'

// Optimized theme store with performance improvements and centralized management
export const useThemeStore = defineStore('theme', {
  state: () => ({
    isGameDev: true, // Default to game dev theme
    isProjectPage: false, // Track if we're on a project page
    projectTheme: null, // Store project-specific theme
    themeClass: '', // Current theme class
    isInitialized: false, // Track if theme has been initialized

    // Performance optimizations
    _cssVariableCache: new Map(), // Cache CSS variable values
    _updateQueue: [], // Queue theme updates for batching
    _updateTimeout: null, // Debounce theme updates
    _observers: new Set(), // Theme change observers
    _isTransitioning: false // Prevent theme flashing during transitions
  }),

  getters: {
    // Cached theme colors for better performance
    themeColors: (state) => {
      const cacheKey = `${state.isGameDev}-${state.isProjectPage}-${JSON.stringify(state.projectTheme)}`

      if (state._cssVariableCache.has(cacheKey)) {
        return state._cssVariableCache.get(cacheKey)
      }

      let colors

      // Project-specific theme colors
      if (state.isProjectPage && state.projectTheme) {
        colors = {
          primary: state.projectTheme.primary || state.projectTheme.colors?.primary,
          secondary: state.projectTheme.secondary || state.projectTheme.colors?.secondary || '#008F11',
          accent: state.projectTheme.accent || state.projectTheme.colors?.accent || '#003B00',
          background: state.projectTheme.background || state.projectTheme.colors?.background || '#121212'
        }
      }
      // Default theme colors
      else {
        colors = state.isGameDev
          ? {
              primary: '#00FF41',    // Matrix green
              secondary: '#008F11',  // Darker green
              accent: '#003B00',     // Very dark green
              background: '#121212'  // Dark background
            }
          : {
              primary: '#00A3FF',    // Bright blue
              secondary: '#0077B6',  // Medium blue
              accent: '#003D5B',     // Dark blue
              background: '#121212'  // Dark background
            }
      }

      // Cache the result
      state._cssVariableCache.set(cacheKey, colors)
      return colors
    },

    // Get current theme class with caching
    currentThemeClass: (state) => {
      if (state.isProjectPage && state.projectTheme?.class) {
        return state.projectTheme.class
      }
      return state.isGameDev ? '' : 'theme-web'
    }
  },

  actions: {
    // Initialize theme with performance optimizations
    async init() {
      if (this.isInitialized) return

      try {
        // Load saved theme preference
        await this._loadThemePreference()

        // Apply initial theme
        await this._applyThemeOptimized()

        this.isInitialized = true
      } catch (error) {
        console.error('Error initializing theme:', error)
        this._fallbackToDefault()
      }
    },

    // Load theme preference from localStorage with error handling
    async _loadThemePreference() {
      if (typeof window === 'undefined') return

      try {
        const savedTheme = localStorage.getItem('themePreference')
        if (savedTheme !== null) {
          this.isGameDev = savedTheme === 'game'
        }
      } catch (error) {
        console.warn('Error loading theme preference:', error)
      }
    },

    // Save theme preference with debouncing
    _saveThemePreference() {
      if (typeof window === 'undefined') return

      // Debounce saves to prevent excessive localStorage writes
      if (this._saveTimeout) {
        clearTimeout(this._saveTimeout)
      }

      this._saveTimeout = setTimeout(() => {
        try {
          const themeValue = this.isGameDev ? 'game' : 'web'
          localStorage.setItem('themePreference', themeValue)
        } catch (error) {
          console.warn('Error saving theme preference:', error)
        }
      }, 100)
    },

    // Optimized theme application with batching
    async _applyThemeOptimized() {
      if (typeof window === 'undefined') return

      // Prevent multiple simultaneous updates
      if (this._isTransitioning) return
      this._isTransitioning = true

      try {
        // Batch DOM updates for better performance
        await this._batchDOMUpdates()

        // Notify observers
        this._notifyObservers()
      } catch (error) {
        console.error('Error applying theme:', error)
        this._fallbackToDefault()
      } finally {
        this._isTransitioning = false
      }
    },

    // Batch DOM updates to prevent layout thrashing
    async _batchDOMUpdates() {
      return new Promise((resolve) => {
        requestAnimationFrame(() => {
          try {
            const colors = this.themeColors
            const themeClass = this.currentThemeClass

            // Update CSS variables efficiently
            this._updateCSSVariables(colors)

            // Update theme classes
            this._updateThemeClasses(themeClass)

            resolve()
          } catch (error) {
            console.error('Error in batch DOM updates:', error)
            resolve()
          }
        })
      })
    },

    // Efficiently update CSS variables
    _updateCSSVariables(colors) {
      if (!document?.documentElement) return

      const root = document.documentElement.style

      // Convert hex to RGB efficiently
      const hexToRgb = (hex) => {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
        return result
          ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
          : '0, 255, 65'
      }

      // Batch CSS variable updates
      const updates = [
        ['--primary-color', colors.primary],
        ['--primary-rgb', hexToRgb(colors.primary)],
        ['--secondary-color', colors.secondary],
        ['--secondary-rgb', hexToRgb(colors.secondary)],
        ['--accent-color', colors.accent],
        ['--accent-rgb', hexToRgb(colors.accent)],
        ['--background-color', colors.background]
      ]

      updates.forEach(([property, value]) => {
        root.setProperty(property, value)
      })
    },

    // Update theme classes efficiently
    _updateThemeClasses(themeClass) {
      if (!document?.documentElement) return

      const classList = document.documentElement.classList

      // Remove all theme classes
      classList.remove('theme-web')

      // Add new theme class if needed
      if (themeClass) {
        classList.add(themeClass)
      }
    },

    // Toggle between game dev and web dev themes
    toggleDevMode() {
      this.isGameDev = !this.isGameDev
      this._saveThemePreference()
      this._applyThemeOptimized()
    },

    // Set project-specific theme
    setProjectTheme(theme) {
      this.isProjectPage = true
      this.projectTheme = theme

      // Clear cache for new theme
      this._cssVariableCache.clear()

      this._applyThemeOptimized()
    },

    // Reset to default theme
    resetToDefaultTheme() {
      this.isProjectPage = false
      this.projectTheme = null

      // Clear cache
      this._cssVariableCache.clear()

      this._applyThemeOptimized()
    },

    // Add theme change observer
    addObserver(callback) {
      this._observers.add(callback)

      // Return unsubscribe function
      return () => {
        this._observers.delete(callback)
      }
    },

    // Notify all observers of theme changes
    _notifyObservers() {
      this._observers.forEach(callback => {
        try {
          callback(this.themeColors, this.currentThemeClass)
        } catch (error) {
          console.warn('Error in theme observer:', error)
        }
      })
    },

    // Fallback to default theme on errors
    _fallbackToDefault() {
      this.isGameDev = true
      this.isProjectPage = false
      this.projectTheme = null
      this._cssVariableCache.clear()

      if (typeof window !== 'undefined' && document?.documentElement) {
        const root = document.documentElement.style
        root.setProperty('--primary-color', '#00FF41')
        root.setProperty('--primary-rgb', '0, 255, 65')
        root.setProperty('--secondary-color', '#008F11')
        root.setProperty('--secondary-rgb', '0, 143, 17')
        root.setProperty('--accent-color', '#003B00')
        root.setProperty('--accent-rgb', '0, 59, 0')
        root.setProperty('--background-color', '#121212')

        document.documentElement.classList.remove('theme-web')
      }
    },

    // Cleanup method for proper memory management
    cleanup() {
      if (this._updateTimeout) {
        clearTimeout(this._updateTimeout)
        this._updateTimeout = null
      }

      if (this._saveTimeout) {
        clearTimeout(this._saveTimeout)
        this._saveTimeout = null
      }

      this._cssVariableCache.clear()
      this._observers.clear()
      this._updateQueue.length = 0
    }
  }
})
