<template>
  <div class="media-carousel relative overflow-hidden rounded-lg shadow-lg" :class="{ 'compact-carousel': true }">
    <!-- Empty state message -->
    <div v-if="items.length === 0" class="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-70 z-30 text-white p-4">
      No items available
    </div>

    <!-- Loading indicator -->
    <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-70 z-20">
      <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2" :style="{ borderColor: carouselColor }"></div>
    </div>

    <!-- Main carousel container -->
    <div class="carousel-container relative" ref="carouselContainer">
      <!-- Slides -->
      <div
        class="carousel-slides flex transition-transform duration-500 ease-out h-full"
        :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
        ref="slidesContainer"
      >
        <div
          v-for="(item, index) in items"
          :key="index"
          class="carousel-slide flex-shrink-0 w-full h-full relative"
        >
          <!-- Image slide -->
          <div v-if="item.type === 'image'" class="gallery-item cursor-pointer" @click="openFullscreen(index)">
            <img
              :src="item.src"
              :alt="item.alt || `Slide ${index + 1}`"
              class="w-full h-full object-contain"
              @load="handleImageLoad"
            />
          </div>

          <!-- Video slide -->
          <video
            v-else-if="item.type === 'video'"
            :src="item.src"
            controls
            class="w-full h-full object-contain"
            @loadeddata="handleImageLoad"
          ></video>
        </div>
      </div>

      <!-- Navigation arrows - smaller for compact view -->
      <button
        v-if="items.length > 1 && showControls"
        @click.stop="prevSlide"
        class="absolute left-1 top-1/2 transform -translate-y-1/2 z-10 p-1 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all duration-300"
        :style="{ color: carouselColor }"
      >
        <IconifyIcon icon="mdi:chevron-left" class="w-4 h-4" />
      </button>

      <button
        v-if="items.length > 1 && showControls"
        @click.stop="nextSlide"
        class="absolute right-1 top-1/2 transform -translate-y-1/2 z-10 p-1 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all duration-300"
        :style="{ color: carouselColor }"
      >
        <IconifyIcon icon="mdi:chevron-right" class="w-4 h-4" />
      </button>

      <!-- Fullscreen button -->
      <button
        v-if="showControls"
        @click.stop="openFullscreen(currentIndex)"
        class="absolute right-1 top-1 z-10 p-1 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all duration-300"
        :style="{ color: carouselColor }"
      >
        <IconifyIcon icon="mdi:fullscreen" class="w-4 h-4" />
      </button>

      <!-- Dots indicator - smaller for compact view -->
      <div
        v-if="items.length > 1 && showDots"
        class="absolute bottom-1 left-0 right-0 flex justify-center gap-1 z-10"
      >
        <button
          v-for="(_, index) in items"
          :key="index"
          @click.stop="goToSlide(index)"
          class="w-1.5 h-1.5 rounded-full transition-all duration-300"
          :class="{ 'scale-125': currentIndex === index }"
          :style="{
            backgroundColor: currentIndex === index ? carouselColor : 'rgba(255, 255, 255, 0.5)',
            boxShadow: currentIndex === index ? `0 0 3px ${carouselColor}` : 'none'
          }"
        ></button>
      </div>
    </div>

    <!-- Fullscreen Gallery -->
    <FullscreenGallery
      :is-open="isFullscreenOpen"
      :items="items"
      :initial-index="fullscreenIndex"
      @close="closeFullscreen"
      @change="updateFullscreenIndex"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { gsap } from 'gsap';
import { useThemeStore } from '~/stores/theme-optimized';
import FullscreenGallery from '~/components/ui/FullscreenGallery.vue';

const props = defineProps({
  items: {
    type: Array,
    required: true,
    default: () => []
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  interval: {
    type: Number,
    default: 5000
  },
  showControls: {
    type: Boolean,
    default: true
  },
  showDots: {
    type: Boolean,
    default: true
  },
  aspectRatio: {
    type: String,
    default: 'video', // 'video' (16:9), 'square', 'custom'
    validator: (value) => ['video', 'square', 'custom'].includes(value)
  }
});

// State
const currentIndex = ref(0);
const loading = ref(true);
const carouselContainer = ref(null);
const slidesContainer = ref(null);
const autoplayInterval = ref(null);
const themeStore = useThemeStore();
const isFullscreenOpen = ref(false);
const fullscreenIndex = ref(0);

// Computed properties
const carouselColor = computed(() => {
  return themeStore.themeColors.primary;
});

// Methods
const nextSlide = () => {
  gsap.to(slidesContainer.value, {
    opacity: 0.8,
    duration: 0.2,
    onComplete: () => {
      currentIndex.value = (currentIndex.value + 1) % props.items.length;
      gsap.to(slidesContainer.value, {
        opacity: 1,
        duration: 0.3
      });
    }
  });
};

const prevSlide = () => {
  gsap.to(slidesContainer.value, {
    opacity: 0.8,
    duration: 0.2,
    onComplete: () => {
      currentIndex.value = (currentIndex.value - 1 + props.items.length) % props.items.length;
      gsap.to(slidesContainer.value, {
        opacity: 1,
        duration: 0.3
      });
    }
  });
};

const goToSlide = (index) => {
  if (index === currentIndex.value) return;

  gsap.to(slidesContainer.value, {
    opacity: 0.8,
    duration: 0.2,
    onComplete: () => {
      currentIndex.value = index;
      gsap.to(slidesContainer.value, {
        opacity: 1,
        duration: 0.3
      });
    }
  });
};

const startAutoplay = () => {
  if (props.autoplay && props.items.length > 1) {
    autoplayInterval.value = setInterval(() => {
      nextSlide();
    }, props.interval);
  }
};

const stopAutoplay = () => {
  if (autoplayInterval.value) {
    clearInterval(autoplayInterval.value);
    autoplayInterval.value = null;
  }
};

const handleImageLoad = () => {
  loading.value = false;
};

// Fullscreen gallery methods
const openFullscreen = (index) => {
  fullscreenIndex.value = index;
  isFullscreenOpen.value = true;
  stopAutoplay(); // Stop autoplay when opening fullscreen
};

const closeFullscreen = () => {
  isFullscreenOpen.value = false;
  startAutoplay(); // Resume autoplay when closing fullscreen
};

const updateFullscreenIndex = (index) => {
  fullscreenIndex.value = index;
};

// Lifecycle hooks
onMounted(() => {
  // Initialize autoplay
  startAutoplay();

  // Add event listeners for hover to pause autoplay
  if (carouselContainer.value) {
    carouselContainer.value.addEventListener('mouseenter', stopAutoplay);
    carouselContainer.value.addEventListener('mouseleave', startAutoplay);
  }

  // Set loading to false after a timeout in case images don't load
  setTimeout(() => {
    loading.value = false;
  }, 2000);
});

onUnmounted(() => {
  // Clean up
  stopAutoplay();

  if (carouselContainer.value) {
    carouselContainer.value.removeEventListener('mouseenter', stopAutoplay);
    carouselContainer.value.removeEventListener('mouseleave', startAutoplay);
  }
});

// Watch for changes in items
watch(() => props.items, () => {
  currentIndex.value = 0;
  loading.value = true;

  // Reset autoplay
  stopAutoplay();
  startAutoplay();
}, { deep: true });
</script>

<style scoped>
.media-carousel {
  min-height: 150px;
  max-height: 300px;
}

.compact-carousel {
  height: 250px;
}

.carousel-container {
  height: 100%;
}

.carousel-slides {
  height: 100%;
}

.carousel-slide {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.5rem;
}

/* Make sure images don't exceed container height */
.carousel-slide img,
.carousel-slide video {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
}

/* Gallery item styling */
.gallery-item {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: zoom-in;
}
</style>
