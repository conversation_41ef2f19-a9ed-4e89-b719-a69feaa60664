<template>
  <div class="youtube-embed-wrapper my-6">
    <div class="youtube-embed relative overflow-hidden rounded-lg shadow-lg" :class="containerClasses">
      <!-- Loading indicator -->
      <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-70 z-20">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2" :style="{ borderColor: themeColor }"></div>
      </div>

      <!-- YouTube iframe -->
      <iframe
        :src="embedUrl"
        class="absolute top-0 left-0 w-full h-full border-0 z-30"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowfullscreen
        @load="loading = false"
      ></iframe>
    </div>

    <slot />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useThemeStore } from '~/stores/theme-optimized';

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  autoplay: {
    type: Boolean,
    default: false
  },
  aspectRatio: {
    type: String,
    default: '16:9',
    validator: (value) => ['16:9', '4:3', '1:1'].includes(value)
  }
});

// State
const loading = ref(true);
const themeStore = useThemeStore();

// Computed properties
const themeColor = computed(() => {
  return themeStore.themeColors.primary;
});

const embedUrl = computed(() => {
  const baseUrl = 'https://www.youtube.com/embed/';
  const params = new URLSearchParams({
    rel: 0,
    showinfo: 0,
    autoplay: props.autoplay ? 1 : 0,
    mute: props.autoplay ? 1 : 0, // Mute if autoplay is enabled (browser requirement)
  });

  return `${baseUrl}${props.id}?${params.toString()}`;
});

const containerClasses = computed(() => {
  const classes = ['aspect-w-16', 'aspect-h-9']; // Default 16:9

  if (props.aspectRatio === '4:3') {
    classes.splice(0, 2, 'aspect-w-4', 'aspect-h-3');
  } else if (props.aspectRatio === '1:1') {
    classes.splice(0, 2, 'aspect-w-1', 'aspect-h-1');
  }

  return classes;
});

// Lifecycle hooks
onMounted(() => {
  // Set loading to false after a timeout in case iframe doesn't trigger load event
  setTimeout(() => {
    loading.value = false;
  }, 3000);

  // Add passive event listeners to document for touch events
  if (typeof document !== 'undefined') {
    const events = ['touchstart', 'touchmove', 'touchend', 'wheel'];
    events.forEach(eventName => {
      document.addEventListener(eventName, () => {}, { passive: true });
    });
  }
});
</script>

<style scoped>
.youtube-embed-wrapper {
  width: 100%;
}

.youtube-embed {
  min-height: 200px;
  position: relative;
  overflow: hidden;
}

/* Aspect ratio classes */
.aspect-w-16.aspect-h-9 {
  aspect-ratio: 16/9;
}

.aspect-w-4.aspect-h-3 {
  aspect-ratio: 4/3;
}

.aspect-w-1.aspect-h-1 {
  aspect-ratio: 1/1;
}
</style>
