<template>
  <h2 class="heading text-2xl font-semibold mt-10 mb-4 pl-3" :style="{ color: 'var(--primary-color)' }">
    <div class="heading-content">
      <slot />
    </div>
    <div class="heading-decoration" :style="{
      backgroundColor: `rgba(var(--primary-rgb), 0.15)`,
      borderLeftColor: 'var(--primary-color)',
      boxShadow: `0 4px 6px -1px rgba(var(--primary-rgb), 0.3)`
    }"></div>
  </h2>
</template>

<script setup>
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
});
</script>

<style scoped>
.heading {
  position: relative;
}

.heading-decoration {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  border-radius: 0.25rem;
  border-left-width: 4px;
  z-index: -1;
}

.heading-content {
  position: relative;
  z-index: 1;
}
</style>
