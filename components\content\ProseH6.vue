<template>
  <h6 class="heading text-sm font-medium mt-4 mb-2 opacity-75" :style="{ color: 'var(--primary-color)' }">
    <div class="heading-content">
      <slot />
    </div>
  </h6>
</template>

<script setup>
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
});
</script>

<style scoped>
.heading {
  position: relative;
}

.heading-content {
  position: relative;
  z-index: 1;
}
</style>
