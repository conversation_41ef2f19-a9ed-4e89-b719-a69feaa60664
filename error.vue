<template>
  <div class="min-h-screen flex items-center justify-center bg-background relative overflow-hidden">
    <!-- Particles background -->
    <ParticlesBackground />

    <!-- Error content -->
    <div class="relative z-10 text-center px-4 max-w-2xl mx-auto">
      <!-- Error code with glow effect -->
      <div class="mb-8">
        <h1 class="text-8xl md:text-9xl font-bold text-primary text-glow mb-4 animate-pulse">
          {{ error.statusCode }}
        </h1>
        <div class="h-1 w-32 bg-gradient-to-r from-transparent via-primary to-transparent mx-auto opacity-60"></div>
      </div>

      <!-- Error message -->
      <div class="mb-12">
        <h2 class="text-2xl md:text-3xl font-semibold text-white mb-4">
          {{ getErrorTitle() }}
        </h2>
        <p class="text-gray-300 text-lg leading-relaxed">
          {{ getErrorMessage() }}
        </p>
      </div>

      <!-- Action buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <!-- Go Home button -->
        <NuxtLink
          to="/"
          class="group relative px-8 py-4 bg-primary/10 border border-primary/30 rounded-lg text-primary font-semibold transition-all duration-300 hover:bg-primary/20 hover:border-primary/50 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary/50 glass"
          @click="handleNavigation"
        >
          <span class="relative z-10 flex items-center gap-2">
            <Icon name="mdi:home" class="w-5 h-5" />
            Go Home
          </span>
          <!-- Hover effect -->
          <div class="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/10 to-primary/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
        </NuxtLink>

        <!-- Go Back button -->
        <button
          @click="goBack"
          class="group relative px-8 py-4 bg-transparent border border-gray-500/30 rounded-lg text-gray-300 font-semibold transition-all duration-300 hover:border-gray-400/50 hover:text-white hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-400/50"
        >
          <span class="relative z-10 flex items-center gap-2">
            <Icon name="mdi:arrow-left" class="w-5 h-5" />
            Go Back
          </span>
        </button>
      </div>

      <!-- Additional help for 404 -->
      <div v-if="error.statusCode === 404" class="mt-12 p-6 glass rounded-lg border border-primary/20">
        <h3 class="text-lg font-semibold text-white mb-4">Looking for something specific?</h3>
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
          <NuxtLink
            to="/projects"
            class="flex items-center gap-2 text-gray-300 hover:text-primary transition-colors duration-200"
            @click="handleNavigation"
          >
            <Icon name="mdi:folder-multiple" class="w-4 h-4" />
            View Projects
          </NuxtLink>
          <NuxtLink
            to="/about"
            class="flex items-center gap-2 text-gray-300 hover:text-primary transition-colors duration-200"
            @click="handleNavigation"
          >
            <Icon name="mdi:account" class="w-4 h-4" />
            About Me
          </NuxtLink>
          <NuxtLink
            to="/contact"
            class="flex items-center gap-2 text-gray-300 hover:text-primary transition-colors duration-200"
            @click="handleNavigation"
          >
            <Icon name="mdi:email" class="w-4 h-4" />
            Contact
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- Decorative elements -->
    <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-primary rounded-full animate-ping opacity-60"></div>
    <div class="absolute bottom-1/3 right-1/4 w-1 h-1 bg-secondary rounded-full animate-pulse opacity-40"></div>
    <div class="absolute top-1/2 right-1/3 w-1.5 h-1.5 bg-accent rounded-full animate-bounce opacity-50"></div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useThemeStore } from '~/stores/theme-optimized'

// Get error from props
const props = defineProps({
  error: {
    type: Object,
    required: true
  }
})

// Theme store
const themeStore = useThemeStore()

// SEO meta
useHead({
  title: computed(() => `${props.error.statusCode} - ${getErrorTitle()}`),
  meta: [
    { name: 'description', content: computed(() => getErrorMessage()) },
    { name: 'robots', content: 'noindex, nofollow' }
  ]
})

// Error title based on status code
const getErrorTitle = () => {
  switch (props.error.statusCode) {
    case 404:
      return 'Page Not Found'
    case 500:
      return 'Internal Server Error'
    case 403:
      return 'Access Forbidden'
    case 401:
      return 'Unauthorized'
    default:
      return 'Something Went Wrong'
  }
}

// Error message based on status code
const getErrorMessage = () => {
  switch (props.error.statusCode) {
    case 404:
      return "The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL."
    case 500:
      return "We're experiencing some technical difficulties. Please try again later or contact support if the problem persists."
    case 403:
      return "You don't have permission to access this resource. Please check your credentials or contact an administrator."
    case 401:
      return "You need to be authenticated to access this page. Please log in and try again."
    default:
      return "An unexpected error occurred. Please try refreshing the page or contact support if the issue continues."
  }
}

// Navigation handlers
const goBack = () => {
  if (typeof window !== 'undefined' && window.history.length > 1) {
    window.history.back()
  } else {
    navigateTo('/')
  }
}

const handleNavigation = () => {
  // Clear any error state
  clearError({ redirect: '/' })
}

// Initialize theme on mount
onMounted(async () => {
  try {
    await themeStore.init()
  } catch (error) {
    console.warn('Error initializing theme on error page:', error)
  }
})

// Log error for debugging (only in development)
if (process.dev) {
  console.error('Error page rendered:', props.error)
}
</script>

<style scoped>
/* Additional styles for the error page */
.text-glow {
  text-shadow:
    0 0 10px rgb(var(--primary-rgb) / 0.6),
    0 0 20px rgb(var(--primary-rgb) / 0.4),
    0 0 30px rgb(var(--primary-rgb) / 0.2);
}

.glass {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Ensure proper spacing and layout */
@media (max-width: 640px) {
  .text-8xl {
    font-size: 4rem;
  }

  .text-9xl {
    font-size: 5rem;
  }
}

/* Animation for decorative elements */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}
</style>
