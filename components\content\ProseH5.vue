<template>
  <h5 class="heading text-base font-medium mt-5 mb-2 opacity-80" :style="{ color: 'var(--primary-color)' }">
    <div class="heading-content">
      <slot />
    </div>
  </h5>
</template>

<script setup>
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
});
</script>

<style scoped>
.heading {
  position: relative;
}

.heading-content {
  position: relative;
  z-index: 1;
}
</style>
