<template>
  <ul class="themed-list">
    <slot />
  </ul>
</template>

<script setup>
import { computed } from 'vue';
import { useThemeStore } from '~/stores/theme-optimized';

const themeStore = useThemeStore();

// Get the primary color from the theme store
const primaryColor = computed(() => themeStore.themeColors.primary);

// Convert hex to rgba
const hexToRgba = (hex, alpha = 1) => {
  if (!hex) return `rgba(0, 255, 65, ${alpha})`;

  // Remove # if present
  hex = hex.replace(/^#/, '');

  // Parse hex values
  let bigint = parseInt(hex, 16);
  let r = (bigint >> 16) & 255;
  let g = (bigint >> 8) & 255;
  let b = bigint & 255;

  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

// Apply the theme color to the list markers
const markerColor = computed(() => hexToRgba(primaryColor.value, 1));
</script>

<style scoped>
.themed-list {
  list-style: none;
  padding-left: 1.5rem;
  margin: 1.25rem 0;
  position: relative;
}

.themed-list :deep(li) {
  position: relative;
  padding-left: 0.5rem;
  margin-bottom: 0.75rem;
  line-height: 1.6;
}

.themed-list :deep(li::before) {
  content: '';
  position: absolute;
  left: -1.25rem;
  top: 0.6rem;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: v-bind('markerColor');
  box-shadow: 0 0 8px v-bind('hexToRgba(primaryColor, 0.6)');
}

/* Add a subtle connecting line between list items */
.themed-list :deep(li:not(:last-child)::after) {
  content: '';
  position: absolute;
  left: -1rem;
  top: 0.85rem;
  width: 1px;
  height: calc(100% + 0.5rem);
  background: linear-gradient(to bottom,
    v-bind('hexToRgba(primaryColor, 0.6)') 0%,
    v-bind('hexToRgba(primaryColor, 0.1)') 100%);
}
</style>
