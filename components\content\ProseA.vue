<template>
  <a
    :href="href"
    :target="isExternal ? '_blank' : undefined"
    :rel="isExternal ? 'noopener noreferrer' : undefined"
    class="hover:opacity-80 transition-opacity duration-300 underline decoration-1 underline-offset-2"
    :style="{ color: 'var(--primary-color)' }"
  >
    <slot />
  </a>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  href: {
    type: String,
    required: true
  }
});

// Check if the link is external
const isExternal = computed(() => {
  return props.href.startsWith('http') || props.href.startsWith('//');
});
</script>
