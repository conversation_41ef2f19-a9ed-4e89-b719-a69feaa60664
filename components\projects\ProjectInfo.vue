<template>
  <div class="project-info">
    <!-- Mobile view with collapsible info panel -->
    <div class="md:hidden">
      <button
        @click="toggleMobileInfo"
        class="w-full flex items-center justify-between p-3 rounded-lg transition-all duration-300"
        :style="{
          backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`,
          borderLeft: `3px solid ${project.meta.colors?.primary || '#00FF41'}`
        }"
      >
        <div class="flex items-center">
          <IconifyIcon icon="mdi:information-outline" class="mr-2 w-5 h-5" />
          <span class="font-medium">Project Details</span>
        </div>
        <IconifyIcon
          :icon="showMobileInfo ? 'mdi:chevron-up' : 'mdi:chevron-down'"
          class="w-5 h-5 transition-transform duration-300"
        />
      </button>

      <!-- Collapsible content -->
      <div
        v-show="showMobileInfo"
        class="mt-3 overflow-hidden transition-all duration-500"
        ref="mobileInfoPanel"
      >
        <GlassContainer class="mb-4">
          <!-- Project Duration -->
          <div v-if="projectDuration" class="mb-4">
            <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Duration</h3>
            <div class="flex items-center gap-2">
              <div
                class="duration-item flex items-center gap-1 px-2 py-1 rounded-full text-xs"
                :style="{
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`,
                  color: project.meta.colors?.primary || '#00FF41'
                }"
              >
                <IconifyIcon icon="mdi:clock-outline" class="w-3.5 h-3.5" :style="{ color: project.meta.colors?.primary || '#00FF41' }" />
                <span>{{ projectDuration }}</span>
              </div>
            </div>
          </div>

          <!-- Platform -->
          <div v-if="projectPlatform" class="mb-4">
            <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Platform</h3>
            <div class="flex flex-wrap gap-2">
              <div
                v-for="(platform, index) in platformList"
                :key="index"
                class="platform-item flex items-center gap-1 px-2 py-1 rounded-full text-xs"
                :style="{
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`,
                  color: project.meta.colors?.primary || '#00FF41'
                }"
              >
                <IconifyIcon :icon="getPlatformIcon(platform)" class="w-3.5 h-3.5" :style="{ color: project.meta.colors?.primary || '#00FF41' }" />
                <span>{{ platform }}</span>
              </div>
            </div>
          </div>

          <!-- Tech stack -->
          <div v-if="project.meta.techStack" class="mb-4">
            <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Tech Stack</h3>
            <div class="flex flex-wrap gap-2">
              <div
                v-for="(tech, index) in project.meta.techStack"
                :key="index"
                class="tech-item flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white"
                :style="{
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
                }"
              >
                <IconifyIcon v-if="tech.icon" :icon="tech.icon" class="w-3.5 h-3.5 text-white" />
                <span>{{ tech.name }}</span>
              </div>
            </div>
          </div>

          <!-- Tags -->
          <div v-if="project.meta.tags" class="mb-4">
            <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Project Type</h3>
            <div class="flex flex-wrap gap-2">
              <div
                v-for="(tag, index) in project.meta.tags"
                :key="index"
                class="tag-item flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white"
                :style="{
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
                }"
              >
                <IconifyIcon v-if="tag.icon" :icon="tag.icon" class="w-3.5 h-3.5 text-white" />
                <span>{{ tag.type }}</span>
              </div>
            </div>
          </div>

          <!-- Roles -->
          <div v-if="project.meta.roles" class="mb-4">
            <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">My Roles</h3>
            <div class="flex flex-wrap gap-2">
              <div
                v-for="(role, index) in project.meta.roles"
                :key="index"
                class="role-item px-2 py-1 rounded-full text-xs"
                :style="{
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`,
                  color: project.meta.colors?.primary || '#00FF41'
                }"
              >
                {{ role }}
              </div>
            </div>
          </div>

          <!-- Project buttons -->
          <div v-if="project.meta.info?.buttons">
            <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Links</h3>
            <div class="flex flex-wrap gap-2">
              <a
                v-for="(button, index) in project.meta.info.buttons"
                :key="index"
                :href="button.url"
                target="_blank"
                rel="noopener noreferrer"
                class="project-button inline-flex items-center px-3 py-1.5 rounded-lg transition-all duration-300 text-sm"
                :style="{
                  backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.2)`,
                  border: `1px solid ${project.meta.colors?.primary || '#00FF41'}`,
                  color: project.meta.colors?.primary || '#00FF41'
                }"
              >
                <span>{{ button.text }}</span>
                <IconifyIcon icon="mdi:open-in-new" class="ml-1.5 w-3.5 h-3.5" :style="{ color: project.meta.colors?.primary || '#00FF41' }" />
              </a>
            </div>
          </div>
        </GlassContainer>
      </div>
    </div>

    <!-- Desktop view with sidebar layout -->
    <div class="hidden md:block">
      <GlassContainer class="sidebar-info">
        <!-- Project Duration -->
        <div v-if="projectDuration" class="mb-6">
          <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Duration</h3>
          <div class="flex items-center gap-2">
            <div
              class="duration-item flex items-center gap-1 px-2 py-1 rounded-full text-xs"
              :style="{
                backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`,
                color: project.meta.colors?.primary || '#00FF41'
              }"
            >
              <IconifyIcon icon="mdi:clock-outline" class="w-3.5 h-3.5" :style="{ color: project.meta.colors?.primary || '#00FF41' }" />
              <span>{{ projectDuration }}</span>
            </div>
          </div>
        </div>

        <!-- Platform -->
        <div v-if="projectPlatform" class="mb-6">
          <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Platform</h3>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="(platform, index) in platformList"
              :key="index"
              class="platform-item flex items-center gap-1 px-2 py-1 rounded-full text-xs"
              :style="{
                backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`,
                color: project.meta.colors?.primary || '#00FF41'
              }"
            >
              <IconifyIcon :icon="getPlatformIcon(platform)" class="w-3.5 h-3.5" :style="{ color: project.meta.colors?.primary || '#00FF41' }" />
              <span>{{ platform }}</span>
            </div>
          </div>
        </div>

        <!-- Tech stack -->
        <div v-if="project.meta.techStack" class="mb-6">
          <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Tech Stack</h3>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="(tech, index) in project.meta.techStack"
              :key="index"
              class="tech-item flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white"
              :style="{
                backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
              }"
            >
              <IconifyIcon v-if="tech.icon" :icon="tech.icon" class="w-3.5 h-3.5 text-white" />
              <span>{{ tech.name }}</span>
            </div>
          </div>
        </div>

        <!-- Tags -->
        <div v-if="project.meta.tags" class="mb-6">
          <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Project Type</h3>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="(tag, index) in project.meta.tags"
              :key="index"
              class="tag-item flex items-center gap-1 px-2 py-1 rounded-full text-xs text-white"
              :style="{
                backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`
              }"
            >
              <IconifyIcon v-if="tag.icon" :icon="tag.icon" class="w-3.5 h-3.5 text-white" />
              <span>{{ tag.type }}</span>
            </div>
          </div>
        </div>

        <!-- Roles -->
        <div v-if="project.meta.roles" class="mb-6">
          <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">My Roles</h3>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="(role, index) in project.meta.roles"
              :key="index"
              class="role-item px-2 py-1 rounded-full text-xs"
              :style="{
                backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.15)`,
                color: project.meta.colors?.primary || '#00FF41'
              }"
            >
              {{ role }}
            </div>
          </div>
        </div>

        <!-- Project buttons -->
        <div v-if="project.meta.info?.buttons">
          <h3 class="text-sm font-semibold uppercase tracking-wider text-gray-400 mb-2">Links</h3>
          <div class="flex flex-col gap-2">
            <a
              v-for="(button, index) in project.meta.info.buttons"
              :key="index"
              :href="button.url"
              target="_blank"
              rel="noopener noreferrer"
              class="project-button inline-flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300 text-sm"
              :style="{
                backgroundColor: `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, 0.2)`,
                border: `1px solid ${project.meta.colors?.primary || '#00FF41'}`,
                color: project.meta.colors?.primary || '#00FF41'
              }"
            >
              <span>{{ button.text }}</span>
              <IconifyIcon icon="mdi:open-in-new" class="ml-1.5 w-3.5 h-3.5" :style="{ color: project.meta.colors?.primary || '#00FF41' }" />
            </a>
          </div>
        </div>
      </GlassContainer>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { gsap } from 'gsap';
import GlassContainer from '~/components/ui/GlassContainer.vue';

const props = defineProps({
  project: {
    type: Object,
    required: true
  }
});

// Convert hex color to RGB values
const hexToRgb = (hex) => {
  // Default color if no hex is provided
  if (!hex) return { r: 66, g: 184, b: 131 }; // Default green color

  // Remove # if present
  hex = hex.replace(/^#/, '');

  // Parse hex values
  let bigint = parseInt(hex, 16);
  let r = (bigint >> 16) & 255;
  let g = (bigint >> 8) & 255;
  let b = bigint & 255;

  return { r, g, b };
};

// State for mobile info panel
const showMobileInfo = ref(false);
const mobileInfoPanel = ref(null);

// Toggle mobile info panel with animation
const toggleMobileInfo = () => {
  showMobileInfo.value = !showMobileInfo.value;

  if (mobileInfoPanel.value) {
    if (showMobileInfo.value) {
      // Animate opening
      gsap.fromTo(mobileInfoPanel.value,
        { height: 0, opacity: 0 },
        { height: 'auto', opacity: 1, duration: 0.3, ease: 'power2.out' }
      );
    } else {
      // Animate closing
      gsap.to(mobileInfoPanel.value, {
        height: 0,
        opacity: 0,
        duration: 0.3,
        ease: 'power2.in'
      });
    }
  }
};

// Extract project duration from info table
const projectDuration = computed(() => {
  if (!props.project?.meta.info?.table) return null;

  // Find the duration entry in the table
  const durationEntry = props.project.meta.info.table.find(item =>
    item.label.toLowerCase() === 'duration'
  );

  return durationEntry ? durationEntry.value : null;
});

// Extract project platform from info table
const projectPlatform = computed(() => {
  if (!props.project?.meta.info?.table) return null;

  // Find the platform entry in the table
  const platformEntry = props.project.meta.info.table.find(item =>
    item.label.toLowerCase() === 'platform'
  );

  return platformEntry ? platformEntry.value : null;
});

// Convert platform string to array of platforms
const platformList = computed(() => {
  if (!projectPlatform.value) return [];

  // Split by commas or slashes and trim whitespace
  return projectPlatform.value
    .split(/[,\/]/)
    .map(platform => platform.trim())
    .filter(platform => platform.length > 0);
});

// Get appropriate icon for platform
const getPlatformIcon = (platform) => {
  const platformLower = platform.toLowerCase();

  if (platformLower.includes('web')) return 'mdi:web';
  if (platformLower.includes('windows')) return 'mdi:microsoft-windows';
  if (platformLower.includes('mac')) return 'mdi:apple';
  if (platformLower.includes('ios')) return 'mdi:apple-ios';
  if (platformLower.includes('android')) return 'mdi:android';
  if (platformLower.includes('linux')) return 'mdi:linux';
  if (platformLower.includes('playstation') || platformLower.includes('ps')) return 'mdi:sony-playstation';
  if (platformLower.includes('xbox')) return 'mdi:microsoft-xbox';
  if (platformLower.includes('nintendo') || platformLower.includes('switch')) return 'mdi:nintendo-switch';
  if (platformLower.includes('mobile')) return 'mdi:cellphone';
  if (platformLower.includes('vr') || platformLower.includes('virtual reality')) return 'mdi:virtual-reality';
  if (platformLower.includes('meta quest') || platformLower.includes('oculus')) return 'ri:meta-fill';

  // Default icon for other platforms
  return 'mdi:devices';
};

// Compute RGB values from project primary color
const rgbValues = computed(() => {
  if (!props.project || !props.project.meta.colors || !props.project.meta.colors.primary) {
    return { r: 66, g: 184, b: 131 }; // Default green color
  }

  const primaryColor = props.project.meta.colors.primary;
  // Remove # if present and ensure it's a valid hex color
  const cleanHex = primaryColor.replace(/^#/, '');
  return hexToRgb(cleanHex);
});

// Initialize animations
onMounted(() => {
  // Set initial state for mobile panel
  if (mobileInfoPanel.value && !showMobileInfo.value) {
    gsap.set(mobileInfoPanel.value, { height: 0, opacity: 0 });
  }
});
</script>

<style scoped>
.project-button {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.project-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.project-button:active {
  transform: translateY(0);
}

.tech-item, .tag-item, .role-item, .duration-item, .platform-item {
  transition: all 0.3s ease;
}

.tech-item:hover, .tag-item:hover, .role-item:hover, .duration-item:hover, .platform-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Sidebar styling */
.sidebar-info {
  position: sticky;
  top: 1.5rem;
}


</style>
