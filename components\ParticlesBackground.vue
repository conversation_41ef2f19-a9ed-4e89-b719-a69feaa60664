<template>
  <div class="fixed inset-0 overflow-hidden particles-background" style="z-index: -1; pointer-events: none;">
    <canvas ref="particleCanvas" class="w-full h-full opacity-80"></canvas>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive, watch, computed } from 'vue';
import { gsap } from 'gsap';
import { useThemeStore } from '~/stores/theme-optimized';
import { useNuxtApp } from '#app';

const particleCanvas = ref(null);
let ctx = null;
let animationFrameId = null;
let columns = [];
// Variables for click handling
let lastClickTime = 0;
// Track all ripple elements for cleanup
let activeRipples = [];

// Get the theme store
const themeStore = useThemeStore();

// Get theme colors from CSS variables instead of store directly
const primaryColor = computed(() => {
  if (typeof window !== 'undefined' && document) {
    try {
      // Get the primary color from CSS variable
      const color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim();
      return color || '#00FF41'; // Default to green if not set
    } catch (error) {
      // Silent fallback to default
      return '#00FF41'; // Default if there's an error
    }
  }
  return '#00FF41'; // Default for SSR
});

const secondaryColor = computed(() => {
  if (typeof window !== 'undefined' && document) {
    try {
      // Get the secondary color from CSS variable
      const color = getComputedStyle(document.documentElement).getPropertyValue('--secondary-color').trim();
      return color || '#008F11'; // Default to darker green if not set
    } catch (error) {
      // Silent fallback to default
      return '#008F11'; // Default if there's an error
    }
  }
  return '#008F11'; // Default for SSR
});

// Configuration
const config = reactive({
  fontSize: 14, // Font size for characters
  columnSpacing: 24, // Space between columns (horizontal spacing)
  verticalSpacing: 20, // Space between characters in a column
  columns: 0,
  rows: 0,
  speed: 1.2, // Speed of falling characters
  density: 0.5, // Percentage of active columns (increased)
  charSpawnRate: 0.12, // Chance to spawn new characters (increased)
  maxCharsPerColumn: 20, // Limit characters per column for performance
  baseColor: '#00FF41', // Default green color
  matrixColor: '#00FF41', // Default green color
  // Characters (mostly binary for matrix effect)
  characters: ['0', '1'],
  // Probability weights for different character types
  charWeights: [
    { chars: ['0', '1'], weight: 0.9 }, // 90% chance of binary
    { chars: ['{', '}', '<', '>'], weight: 0.1 } // 10% chance of brackets
  ],
  // Matrix effect settings
  leaderProbability: 0.3, // Chance for a character to be a "leader" (brighter)
  trailLength: 6, // Number of characters in a trail
  // Shockwave effect settings
  shockwaveSpeed: 5,
  shockwaveSize: 1500,
  shockwaveDuration: 1.5
});

// Character column class
class Column {
  constructor(x, index) {
    this.x = x;
    this.index = index;
    this.chars = [];
    this.startingY = Math.random() * -1000;
    this.speed = (Math.random() * 0.5 + 0.5) * config.speed;
    this.active = Math.random() < config.density;
    this.nextCharTime = 0; // Time tracker for character spawning
    this.hasLeader = false; // Whether this column has a leader character
    this.leaderIndex = -1; // Index of the leader character

    // Add initial characters for a more filled look
    if (this.active) {
      const initialChars = Math.floor(Math.random() * 5) + 2; // At least 2 characters
      for (let i = 0; i < initialChars; i++) {
        // First character has a chance to be a leader
        const isLeader = i === 0 && Math.random() < config.leaderProbability;

        if (isLeader) {
          this.hasLeader = true;
          this.leaderIndex = 0;
        }

        this.chars.push({
          value: this.getWeightedRandomChar(),
          y: this.startingY + (i * config.verticalSpacing),
          // Matrix-style trailing effect - first character is brightest
          alpha: isLeader ? 1 : Math.max(0.2, 1 - (i * 0.15)),
          isLeader: isLeader,
          // Track if this character is affected by shockwave
          shockwaveAffected: false
        });
      }
    }
  }

  // Get a random character based on weights
  getWeightedRandomChar() {
    // Generate a random number between 0 and 1
    const rand = Math.random();
    let cumulativeWeight = 0;

    // Find which weight group this random number falls into
    for (const group of config.charWeights) {
      cumulativeWeight += group.weight;
      if (rand < cumulativeWeight) {
        // Select a random character from this group
        const chars = group.chars;
        return chars[Math.floor(Math.random() * chars.length)];
      }
    }

    // Fallback to binary
    return Math.random() < 0.5 ? '0' : '1';
  }

  update(deltaTime) {
    if (!this.active) return;

    // Increment time tracker
    this.nextCharTime += deltaTime;

    // Add new character at the top with increased frequency
    // Use time-based spawning for more consistent rate
    if (this.nextCharTime > 60 / config.charSpawnRate) {
      this.nextCharTime = 0;

      // Only add if we're under the max chars limit
      if (this.chars.length < config.maxCharsPerColumn) {
        // Determine if this should be a leader character
        const isLeader = !this.hasLeader && Math.random() < config.leaderProbability;

        // Use weighted random selection for characters
        const char = this.getWeightedRandomChar();

        // Add the new character at the top
        this.chars.unshift({
          value: char,
          y: this.startingY,
          alpha: isLeader ? 1 : 0.8, // Leaders are brighter
          isLeader: isLeader,
          shockwaveAffected: false
        });

        // Update leader tracking
        if (isLeader) {
          this.hasLeader = true;
          this.leaderIndex = 0;
        } else if (this.leaderIndex >= 0) {
          // Shift the leader index when adding new chars
          this.leaderIndex++;
        }
      }
    }

    // Update existing characters
    for (let i = 0; i < this.chars.length; i++) {
      const char = this.chars[i];

      // Move character down
      char.y += this.speed * deltaTime;

      // Matrix-style trailing effect - characters after the leader are dimmer
      if (this.hasLeader && this.leaderIndex >= 0) {
        const distanceFromLeader = i - this.leaderIndex;

        if (distanceFromLeader > 0 && distanceFromLeader < config.trailLength) {
          // Characters in the trail fade out gradually
          const trailFactor = 1 - (distanceFromLeader / config.trailLength);
          char.alpha = 0.3 + (trailFactor * 0.7); // Scale between 0.3 and 1.0
        } else if (distanceFromLeader >= config.trailLength) {
          // Characters beyond the trail are dim
          char.alpha = 0.3;
        }
      }

      // Fade out as they fall off screen
      if (char.y > config.rows * config.verticalSpacing) {
        char.alpha -= 0.02 * deltaTime; // Faster fade out

        // If this was the leader, update leader tracking
        if (char.isLeader && char.alpha <= 0) {
          this.hasLeader = false;
          this.leaderIndex = -1;
        }
      }

      // Check if this character is affected by a shockwave
      if (char.shockwaveAffected) {
        // Apply shockwave effect (handled in click handler)
        // This is just a placeholder for the property
      }
    }

    // Remove faded out characters
    this.chars = this.chars.filter(char => char.alpha > 0);

    // Update leader index after removing characters
    if (this.hasLeader) {
      const leaderChar = this.chars.findIndex(char => char.isLeader);
      this.leaderIndex = leaderChar >= 0 ? leaderChar : -1;
      this.hasLeader = this.leaderIndex >= 0;
    }
  }

  draw() {
    if (!this.active) return;

    for (const char of this.chars) {
      // Determine color based on character type and leader status
      let color;

      if (char.isLeader) {
        // Leader characters are bright matrix green
        color = `rgba(${hexToRgb(config.matrixColor)}, ${char.alpha})`;
      } else if (char.shockwaveAffected) {
        // Shockwave affected characters are white
        color = `rgba(255, 255, 255, ${char.alpha})`;
      } else {
        // Different colors for different character types
        if (char.value === '0' || char.value === '1') {
          // Binary digits in matrix green but dimmer
          color = `rgba(${hexToRgb(config.matrixColor)}, ${char.alpha * 0.8})`;
        } else {
          // Other characters in base color
          color = `rgba(${hexToRgb(config.baseColor)}, ${char.alpha * 0.7})`;
        }
      }

      // Set the font and color
      ctx.font = `${config.fontSize}px "Courier New", monospace`;
      ctx.fillStyle = color;

      // Draw the character
      ctx.fillText(char.value, this.x, char.y);
    }
  }
}

// Helper function to convert hex to rgb
function hexToRgb(hex) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result
    ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
    : '66, 184, 131'; // Default Vue green
}

// Initialize the canvas
function initCanvas() {
  try {
    if (!particleCanvas.value) {
      console.warn('Canvas element not available yet');
      return;
    }

    ctx = particleCanvas.value.getContext('2d');
    if (!ctx) {
      console.warn('Could not get 2D context from canvas');
      return;
    }

    // Set canvas dimensions
    const updateCanvasSize = () => {
      try {
        particleCanvas.value.width = window.innerWidth;
        particleCanvas.value.height = window.innerHeight;

        // Calculate columns and rows with proper spacing
        config.columns = Math.ceil(particleCanvas.value.width / config.columnSpacing);
        config.rows = Math.ceil(particleCanvas.value.height / config.verticalSpacing);

        // Create columns with proper spacing
        columns = [];
        for (let i = 0; i < config.columns; i++) {
          columns.push(new Column(i * config.columnSpacing, i));
        }
      } catch (error) {
        console.error('Error updating canvas size:', error);
      }
    };

    // Initial setup
    updateCanvasSize();

    // Handle resize
    window.addEventListener('resize', updateCanvasSize);

    // Handle mouse movement
    window.addEventListener('mousemove', handleMouseMove);

    // Handle click for a more dramatic effect
    window.addEventListener('click', handleClick);

    // Start animation
    let lastTime = 0;
    const animate = (timestamp) => {
      try {
        if (!particleCanvas.value || !ctx) return;

        const deltaTime = timestamp - lastTime || 0;
        lastTime = timestamp;

        // Clear canvas
        ctx.clearRect(0, 0, particleCanvas.value.width, particleCanvas.value.height);

        // Update and draw columns
        for (const column of columns) {
          column.update(deltaTime / 16); // Normalize to ~60fps
          column.draw();
        }

        animationFrameId = requestAnimationFrame(animate);
      } catch (error) {
        console.error('Error in animation loop:', error);
        // Try to recover by requesting next frame
        animationFrameId = requestAnimationFrame(animate);
      }
    };

    animate(0);
  } catch (error) {
    console.error('Error initializing canvas:', error);
  }
}

// We don't need mouse movement tracking anymore
function handleMouseMove() {
  // Empty function - we keep it to avoid errors when removing event listeners
}

// Function to clean up all ripple elements
function cleanupRipples() {
  try {
    // Remove all active ripples from the DOM
    activeRipples.forEach(ripple => {
      try {
        if (ripple && ripple.parentNode) {
          ripple.parentNode.removeChild(ripple);
        }
      } catch (error) {
        // Silent fallback
      }
    });

    // Clear the array
    activeRipples = [];
  } catch (error) {
    // Silent fallback
  }
}

// Watch for theme changes to update particle colors
watch(
  [primaryColor, secondaryColor, () => themeStore.isGameDev, () => themeStore.themeColors],
  ([newPrimary]) => {
    try {
      // Update the particle colors with string values
      if (newPrimary && typeof newPrimary === 'string') {
        config.baseColor = newPrimary;
        config.matrixColor = newPrimary;
      }
    } catch (error) {
      // Silent fallback
    }
  },
  { immediate: false, deep: true } // Don't apply immediately, wait for component to mount
);

// Also watch themeColors directly as a fallback
watch(
  () => themeStore.themeColors,
  (newColors) => {
    try {
      if (newColors && newColors.primary) {
        config.baseColor = newColors.primary;
        config.matrixColor = newColors.primary;

        // Update any existing ripple elements with the new color
        if (typeof document !== 'undefined') {
          try {
            const ripples = document.querySelectorAll('.ripple');
            if (ripples.length > 0) {
              const primaryRgb = hexToRgb(newColors.primary);
              ripples.forEach(ripple => {
                ripple.style.backgroundColor = `rgba(${primaryRgb}, 0.15)`;
                ripple.style.border = `2px solid rgba(${primaryRgb}, 0.4)`;
                ripple.style.boxShadow = `0 0 30px rgba(${primaryRgb}, 0.3)`;
              });
            }
          } catch (error) {
            // Silent fallback
          }
        }
      }
    } catch (error) {
      // Silent fallback
    }
  },
  { immediate: false, deep: true }
);

// Watch for project theme changes specifically
watch(
  [() => themeStore.isProjectPage, () => themeStore.projectTheme],
  ([isProjectPage, projectTheme]) => {
    try {

      if (isProjectPage && projectTheme && typeof projectTheme === 'object' && projectTheme.primary) {
        // Update particle colors with project theme
        config.baseColor = projectTheme.primary;
        config.matrixColor = projectTheme.primary;

        // Update any existing ripple elements with the project color
        if (typeof document !== 'undefined') {
          try {
            const ripples = document.querySelectorAll('.ripple');
            if (ripples.length > 0) {
              const primaryRgb = hexToRgb(projectTheme.primary);
              ripples.forEach(ripple => {
                ripple.style.backgroundColor = `rgba(${primaryRgb}, 0.15)`;
                ripple.style.border = `2px solid rgba(${primaryRgb}, 0.4)`;
                ripple.style.boxShadow = `0 0 30px rgba(${primaryRgb}, 0.3)`;
              });
            }
          } catch (error) {
            // Silent fallback
          }
        }
      }
    } catch (error) {
      // Silent fallback
    }
  },
  { immediate: true, deep: true }
);

// Get Nuxt app instance for hooks
const nuxtApp = useNuxtApp();

// Add hook for page transitions to clean up ripples
if (nuxtApp && nuxtApp.hook) {
  nuxtApp.hook('page:start', () => {
    // Clean up ripples when navigation starts
    cleanupRipples();
  });
}

onMounted(() => {
  try {
    // First, try to get colors from the theme store
    const themeColors = themeStore.themeColors;
    if (themeColors && themeColors.primary) {
      config.baseColor = themeColors.primary;
      config.matrixColor = themeColors.primary;
    }
    // Fallback to CSS variables if theme store doesn't have colors
    else {
      const initialPrimary = primaryColor.value;
      if (initialPrimary && typeof initialPrimary === 'string') {
        config.baseColor = initialPrimary;
        config.matrixColor = initialPrimary;
      }
    }

    // Use a longer timeout to ensure the DOM is fully rendered and CSS variables are applied
    // This is especially important when navigating directly to non-home routes
    setTimeout(() => {
      try {
        initCanvas();

        // Force a color update after initialization
        setTimeout(() => {
          try {
            // Get the latest color from CSS variables
            const currentPrimary = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim();
            if (currentPrimary) {
              config.baseColor = currentPrimary;
              config.matrixColor = currentPrimary;
            }
          } catch (error) {
            // Silent fallback
          }
        }, 500);
      } catch (error) {
        // Silent fallback
      }
    }, 300);

    // Clean up any existing ripples
    cleanupRipples();
  } catch (error) {
    // Silent fallback
  }
});

// Handle click event for a shockwave effect
function handleClick(e) {
  try {
    // Prevent too many clicks in a short time (throttle)
    const now = Date.now();
    if (now - lastClickTime < 800) return; // At least 800ms between clicks
    lastClickTime = now;

    // Create a shockwave ripple effect
    const ripple = document.createElement('div');
    ripple.className = 'ripple';
    ripple.style.position = 'fixed';
    ripple.style.left = `${e.clientX}px`;
    ripple.style.top = `${e.clientY}px`;

    // Get RGB values from the current theme color for the ripple effect
    let primaryRgb;
    try {
      // First check if we're on a project page
      if (themeStore.isProjectPage && themeStore.projectTheme && typeof themeStore.projectTheme === 'object') {
        primaryRgb = hexToRgb(themeStore.projectTheme.primary);
      }
      // Then try to get from theme store
      else if (themeStore.themeColors && themeStore.themeColors.primary) {
        primaryRgb = hexToRgb(themeStore.themeColors.primary);
      }
      // Fallback to computed property
      else if (primaryColor.value) {
        primaryRgb = hexToRgb(primaryColor.value);
      }
      // Fallback to CSS variable
      else {
        const cssColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim();
        primaryRgb = hexToRgb(cssColor || '#00FF41');
      }
    } catch (error) {
      // Silent fallback to default
      primaryRgb = '0, 255, 65'; // Default green
    }

    ripple.style.backgroundColor = `rgba(${primaryRgb}, 0.15)`; // Theme color with low opacity
    ripple.style.borderRadius = '50%';
    ripple.style.pointerEvents = 'none';
    ripple.style.zIndex = '-5';
    ripple.style.border = `2px solid rgba(${primaryRgb}, 0.4)`; // Ring effect with theme color
    ripple.style.boxShadow = `0 0 30px rgba(${primaryRgb}, 0.3)`; // Enhanced glow with theme color
    document.body.appendChild(ripple);

    // Add to active ripples array for tracking
    activeRipples.push(ripple);

  // Animate the shockwave with GSAP
  try {
    gsap.fromTo(ripple,
      { width: 0, height: 0, opacity: 0.9 },
      {
        width: config.shockwaveSize,
        height: config.shockwaveSize,
        opacity: 0,
        x: -config.shockwaveSize/2,
        y: -config.shockwaveSize/2,
        duration: config.shockwaveDuration,
        ease: 'power2.out',
        onComplete: () => {
          try {
            // Remove from DOM
            if (ripple && ripple.parentNode) {
              ripple.parentNode.removeChild(ripple);
            }

            // Remove from tracking array
            const index = activeRipples.indexOf(ripple);
            if (index !== -1) {
              activeRipples.splice(index, 1);
            }
          } catch (error) {
            // Silent fallback
          }
        }
      }
    );

    // Affect characters within the shockwave radius
    const clickX = e.clientX;
    const clickY = e.clientY;

    // Process all columns
    if (columns && columns.length > 0) {
      for (const column of columns) {
        if (!column || !column.active) continue;

        // Calculate horizontal distance from click
        const dx = column.x - clickX;
        const distanceX = Math.abs(dx);

        // Only process columns within reasonable distance
        if (distanceX < config.shockwaveSize / 2) {
          // Process characters in this column
          for (const char of column.chars) {
            // Calculate vertical distance from click
            const dy = char.y - clickY;
            const distance = Math.sqrt(dx * dx + dy * dy);

            // If within shockwave radius
            if (distance < config.shockwaveSize / 2) {
              // Mark as affected by shockwave
              char.shockwaveAffected = true;

              // Calculate delay based on distance (further = later)
              const delay = (distance / (config.shockwaveSize / 2)) * (config.shockwaveDuration * 0.5);

              // Animate the character with GSAP
              gsap.to(char, {
                alpha: 1, // Brighten
                delay: delay,
                duration: 0.2,
                ease: 'power1.in',
                onComplete: () => {
                  // Then fade back to normal
                  gsap.to(char, {
                    alpha: char.isLeader ? 1 : 0.8,
                    duration: 0.5,
                    ease: 'power1.out',
                    onComplete: () => {
                      char.shockwaveAffected = false;
                    }
                  });
                }
              });
            }
          }
        }
      }

      // Add a few new characters at the click point for visual effect
      const burstCount = 5;

      for (let i = 0; i < burstCount; i++) {
        // Find the closest column to the click
        const columnIndex = Math.floor(clickX / config.columnSpacing);

        // Get a range of columns around the click
        const range = 3; // 3 columns on either side
        const randomOffset = Math.floor(Math.random() * range * 2) - range;
        const targetColumnIndex = Math.max(0, Math.min(config.columns - 1, columnIndex + randomOffset));

        // If we have a valid column, add a character
        if (columns[targetColumnIndex]) {
          const column = columns[targetColumnIndex];
          if (column.active) {
            // Add a character at click position with random offset
            const yOffset = (Math.random() - 0.5) * 50;
            column.chars.push({
              value: column.getWeightedRandomChar(),
              y: clickY + yOffset,
              alpha: 1,
              isLeader: false,
              shockwaveAffected: true
            });
          }
        }
      }
    }
  } catch (error) {
    // Silent fallback
  }
  } catch (error) {
    // Silent fallback
  }
}

onUnmounted(() => {
  try {
    // Cancel animation frame
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
    }

    // Remove event listeners
    window.removeEventListener('resize', () => {});
    window.removeEventListener('mousemove', handleMouseMove);
    window.removeEventListener('click', handleClick);

    // Clean up any remaining ripples
    cleanupRipples();
  } catch (error) {
    // Silent fallback
  }
});
</script>

<style scoped>
.ripple {
  position: absolute;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.particles-background {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
  z-index: -1 !important;
  pointer-events: none;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
}
</style>