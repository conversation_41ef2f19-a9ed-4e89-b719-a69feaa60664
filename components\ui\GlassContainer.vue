<template>
  <div class="glass-container" :class="[rounded, padding, className]">
    <slot></slot>
  </div>
</template>

<script setup>
import { onMounted, watch } from 'vue';
import { useThemeStore } from '~/stores/theme-optimized';

defineProps({
  rounded: {
    type: String,
    default: 'rounded-lg'
  },
  padding: {
    type: String,
    default: 'p-4 md:p-8'
  },
  className: {
    type: String,
    default: ''
  }
});

const themeStore = useThemeStore();

// Function to update CSS variables based on theme
const updateGlassColors = () => {
  try {
    // Check if we're in a browser environment
    if (typeof window === 'undefined' || !document || !document.documentElement) {
      return; // Exit if not in browser environment
    }

    // Get primary color with fallback
    let primaryColor;
    try {
      // First try to get from theme store
      if (themeStore.themeColors && themeStore.themeColors.primary) {
        primaryColor = themeStore.themeColors.primary;
      } else {
        // Fallback to CSS variable
        primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim() || '#00FF41';
      }
    } catch (error) {
      // Silent fallback to default
      primaryColor = '#00FF41'; // Default to green
    }

    // Extract RGB values from hex color
    const hexToRgb = (hex) => {
      try {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result
          ? {
              r: parseInt(result[1], 16),
              g: parseInt(result[2], 16),
              b: parseInt(result[3], 16)
            }
          : { r: 0, g: 255, b: 65 }; // Default green
      } catch (error) {
        // Silent fallback to default
        return { r: 0, g: 255, b: 65 }; // Default green
      }
    };

    const rgb = hexToRgb(primaryColor);

    // Set CSS variables for the glass effect
    document.documentElement.style.setProperty(
      '--light-gradient-color-1',
      `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.01)`
    );
    document.documentElement.style.setProperty(
      '--light-gradient-color-2',
      `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.02)`
    );
  } catch (error) {
    // Silent fallback
  }
};

// Update colors when theme changes - but only in client-side
watch(() => themeStore.themeColors, () => {
  try {
    // This will only execute the update if we're in a browser environment
    updateGlassColors();
  } catch (error) {
    // Silent fallback
  }
}, { deep: true, immediate: false });

// Also watch for isGameDev changes as a fallback
watch(() => themeStore.isGameDev, () => {
  try {
    // Delay slightly to ensure CSS variables are updated
    setTimeout(() => {
      updateGlassColors();
    }, 50);
  } catch (error) {
    // Silent fallback
  }
}, { immediate: false });

onMounted(() => {
  try {
    // Set initial colors with a delay to ensure theme is initialized
    setTimeout(() => {
      updateGlassColors();
    }, 100);
  } catch (error) {
    // Silent fallback
  }
});
</script>

<!-- Styles moved to assets/css/tailwind.css for better SEO and performance -->
