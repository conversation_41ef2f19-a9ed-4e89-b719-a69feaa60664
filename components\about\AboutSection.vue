<template>
  <div class="snap-section flex flex-col justify-center px-4 relative h-full">
    <div class="w-full max-w-lg md:max-w-2xl mx-auto">
      <div class="text-center">
        <h1
          v-motion
          :initial="{ opacity: 0 }"
          :enter="{ opacity: 1, transition: { duration: 600 } }"
          class="text-3xl md:text-5xl font-bold mb-6 md:mb-8"
          :style="{ color: themeStore.themeColors.primary }"
        >
          About Me
        </h1>

        <GlassContainer
          v-motion
          :initial="{ opacity: 0 }"
          :enter="{ opacity: 1, transition: { duration: 600, delay: 300 } }"
          class="mb-8 md:mb-10 relative z-10"
        >
          <p class="text-base md:text-xl" :style="{ color: getDarkenedPrimary() }">
            I'm a passionate developer with expertise in both web development and game development.
            With a strong foundation in modern technologies, I create engaging digital experiences
            that combine creativity with technical excellence.
          </p>
        </GlassContainer>
      </div>
    </div>
  </div>
</template>

<script setup>
import GlassContainer from '../ui/GlassContainer.vue';
import { useThemeStore } from '~/stores/theme-optimized';

// Get the theme store
const themeStore = useThemeStore();

// Helper function to convert hex to RGB
const hexToRgb = (hex) => {
  // Remove # if present
  hex = hex.replace('#', '');

  // Handle shorthand hex
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }

  // Parse hex values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return { r, g, b };
};

// Function to get a darkened version of the primary color for better readability
const getDarkenedPrimary = () => {
  const primary = themeStore.themeColors.primary || (themeStore.isGameDev ? '#00FF41' : '#00A3FF');
  const rgb = hexToRgb(primary);

  // Darken the color by reducing each RGB component by 30%
  const darkenedR = Math.max(0, Math.floor(rgb.r * 0.7));
  const darkenedG = Math.max(0, Math.floor(rgb.g * 0.7));
  const darkenedB = Math.max(0, Math.floor(rgb.b * 0.7));

  return `rgb(${darkenedR}, ${darkenedG}, ${darkenedB})`;
};
</script>

<style scoped>
.snap-section {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

</style>
