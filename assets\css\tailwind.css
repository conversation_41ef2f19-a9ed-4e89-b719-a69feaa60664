@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Default CSS variables for both client and server rendering */
  :root {
    /* Main colors */
    --primary-color: #00FF41;    /* Matrix green */
    --secondary-color: #008F11;  /* Darker green */
    --accent-color: #003B00;     /* Very dark green */
    --background-color: #121212; /* Dark background */

    /* RGB values for rgba usage */
    --primary-rgb: 0, 255, 65;
    --secondary-rgb: 0, 143, 17;
    --accent-rgb: 0, 59, 0;

    /* Transition for smooth theme changes */
    --theme-transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
  }

  /* Web Dev Theme (Blue) */
  html.theme-web {
    /* Main colors */
    --primary-color: #00A3FF;    /* Bright blue */
    --secondary-color: #0077B6;  /* Medium blue */
    --accent-color: #003D5B;     /* Dark blue */

    /* RGB values for rgba usage */
    --primary-rgb: 0, 163, 255;
    --secondary-rgb: 0, 119, 182;
    --accent-rgb: 0, 61, 91;
  }

  /* Base styles */
  html {
    overflow-x: hidden;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    transition: background-color 0.5s ease;
  }

  body {
    overflow-x: hidden;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }

  /* No scroll class for home page only */
  body.no-scroll-home {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
  }

  /* Add padding to main content to account for fixed navbar */
  main {
    padding-top: 4rem; /* 64px for mobile */
  }

  /* Remove padding for project detail page */
  main.project-detail-page {
    padding-top: 0;
  }

  /* Responsive adjustments */
  @media (min-width: 768px) {
    main {
      padding-top: 5rem; /* 80px for desktop */
    }

    main.project-detail-page {
      padding-top: 0;
    }
  }
}

@layer components {
  /* Glass effect components */
  .glass {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-light {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Glass container with thick glass effect */
  .glass-container {
    position: relative;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
    border: none;
    box-shadow:
      0 4px 30px rgba(0, 0, 0, 0.2),
      inset 0 0 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    isolation: isolate;
  }

  /* Glass edge highlight effect */
  .glass-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    border-radius: inherit;
    border-top: 1px solid rgba(255, 255, 255, 0.07);
    border-left: 1px solid rgba(255, 255, 255, 0.07);
    border-right: 1px solid rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(0, 0, 0, 0.3);
    pointer-events: none;
  }

  /* Loading spinner */
  .loading-spinner {
    border: 4px solid transparent;
    border-top: 4px solid rgb(var(--primary-rgb));
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  /* Button styles */
  .btn-primary {
    @apply px-6 py-3 bg-primary/10 border border-primary/30 rounded-lg text-primary font-semibold transition-all duration-300 hover:bg-primary/20 hover:border-primary/50 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary/50;
  }

  .btn-secondary {
    @apply px-6 py-3 bg-transparent border border-gray-500/30 rounded-lg text-gray-300 font-semibold transition-all duration-300 hover:border-gray-400/50 hover:text-white hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-400/50;
  }

  /* Input styles */
  .input-field {
    @apply w-full px-4 py-3 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 transition-all duration-300 focus:outline-none focus:border-primary/50 focus:ring-2 focus:ring-primary/20;
  }

  .input-error {
    @apply border-red-500/50 focus:border-red-500/70 focus:ring-red-500/20;
  }

  /* Logo gradient */
  .logo-gradient {
    background: linear-gradient(to right, rgb(var(--primary-rgb)), rgb(var(--secondary-rgb)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Navigation styles */
  .nav-link {
    @apply relative px-4 py-2 text-gray-300 transition-all duration-300 hover:text-primary;
  }

  .nav-link.active {
    @apply text-primary;
  }

  .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, transparent, rgb(var(--primary-rgb)), transparent);
    transition: all 0.3s ease;
    transform: translateX(-50%);
  }

  .nav-link:hover::after,
  .nav-link.active::after {
    width: 100%;
  }
}

@layer utilities {
  /* Text effects */
  .text-glow {
    text-shadow: 0 0 10px rgb(var(--primary-rgb) / 0.6);
  }

  .text-glow-strong {
    text-shadow:
      0 0 10px rgb(var(--primary-rgb) / 0.6),
      0 0 20px rgb(var(--primary-rgb) / 0.4),
      0 0 30px rgb(var(--primary-rgb) / 0.2);
  }

  /* Border effects */
  .border-glow {
    box-shadow: 0 0 10px rgb(var(--primary-rgb) / 0.3);
  }

  .border-glow-strong {
    box-shadow:
      0 0 10px rgb(var(--primary-rgb) / 0.5),
      0 0 20px rgb(var(--primary-rgb) / 0.3),
      inset 0 0 10px rgb(var(--primary-rgb) / 0.1);
  }

  /* Dynamic border colors */
  .border-dynamic {
    border-color: rgb(var(--primary-rgb));
  }

  .border-dynamic-secondary {
    border-color: rgb(var(--secondary-rgb));
  }

  /* Dynamic text colors */
  .text-dynamic {
    color: rgb(var(--primary-rgb));
  }

  .text-dynamic-secondary {
    color: rgb(var(--secondary-rgb));
  }

  /* Dynamic background colors */
  .bg-dynamic {
    background-color: rgb(var(--primary-rgb));
  }

  .bg-dynamic-secondary {
    background-color: rgb(var(--secondary-rgb));
  }

  /* Focus ring with dynamic color */
  .focus-ring-dynamic {
    --focus-ring-color: rgb(var(--primary-rgb));
  }

  /* Animations */
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  /* Responsive text sizes */
  .text-responsive-xl {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  .text-responsive-2xl {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  .text-responsive-3xl {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  /* Keyframes for animations */
  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes glow {
    0% { text-shadow: 0 0 5px rgb(var(--primary-rgb) / 0.5); }
    100% { text-shadow: 0 0 20px rgb(var(--primary-rgb) / 0.8); }
  }

  @keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
  }

  @keyframes slideUp {
    0% { transform: translateY(20px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }

  @keyframes scaleIn {
    0% { transform: scale(0.9); opacity: 0; }
    100% { transform: scale(1); opacity: 1; }
  }
}

/* Theme-specific classes for consistent hydration */
.project-theme-gradient {
  /* Default game dev theme (green) */
  @apply bg-gradient-to-r from-green-400 to-green-600;
  background-clip: text;
  -webkit-background-clip: text;
  background: linear-gradient(to right, var(--primary-color, #00FF41), var(--secondary-color, #008F11));
  -webkit-text-fill-color: transparent;
}

.project-theme-text {
  /* Default game dev theme (green) */
  @apply text-green-400;
  color: var(--primary-color, #00FF41);
}

.project-theme-border {
  /* Default game dev theme (green) */
  @apply border-green-400;
  border-color: var(--primary-color, #00FF41);
}

/* Web theme variants */
html.theme-web .project-theme-gradient {
  @apply bg-gradient-to-r from-blue-400 to-blue-600;
  background: linear-gradient(to right, var(--primary-color, #00A3FF), var(--secondary-color, #0077B6));
}

html.theme-web .project-theme-text {
  @apply text-blue-400;
  color: var(--primary-color, #00A3FF);
}

html.theme-web .project-theme-border {
  @apply border-blue-400;
  border-color: var(--primary-color, #00A3FF);
}

/* Super fancy page transitions */
.page-enter-active {
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.page-leave-active {
  transition: all 0.4s cubic-bezier(0.36, 0, 0.66, -0.56);
}

.page-enter-from {
  opacity: 0;
  filter: blur(1rem);
  transform: scale(1.1) translateY(-10px);
}

.page-leave-to {
  opacity: 0;
  filter: blur(1rem);
  transform: scale(0.9) translateY(10px);
}

.prose {
  max-width: 75ch;
  color: white;
}

.prose h1 {
  color: var(--primary-color) !important;
}

.prose h2 {
  color: var(--primary-color) !important;
}

.prose h3 {
  color: var(--primary-color) !important;
}

.prose h4 {
  color: var(--primary-color) !important;
}

.prose h5 {
  color: var(--primary-color) !important;
}

.prose h6 {
  color: var(--primary-color) !important;
}

.prose strong {
  color: var(--primary-color) !important;
}

.prose p {
  color: white !important;
}

.prose li {
  color: white !important;
}

.prose a {
  color: var(--primary-color) !important;
}