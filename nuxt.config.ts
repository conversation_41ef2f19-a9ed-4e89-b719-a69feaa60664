// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-04-03',

  // Performance optimizations
  ssr: false, // Disable SSR for complex animations and dynamic content

  // Optimized modules loading
  modules: [
    '@nuxt/content',
    '@nuxtjs/tailwindcss',
    '@vueuse/motion/nuxt',
    '@pinia/nuxt',
    '@nuxtjs/sitemap',
    '@nuxtjs/robots'
  ],

  // Vite optimizations for better performance
  vite: {
    build: {
      // Enable code splitting for better caching
      rollupOptions: {
        output: {
          manualChunks: {
            // Separate vendor chunks for better caching
            'gsap': ['gsap'],
            'locomotive-scroll': ['locomotive-scroll'],
            'three': ['three'],
            'pinia': ['pinia'],
            'vue-router': ['vue-router']
          }
        }
      },
      // Optimize chunk size
      chunkSizeWarningLimit: 1000,
    },
    optimizeDeps: {
      // Pre-bundle these dependencies
      include: [
        'gsap',
        'locomotive-scroll',
        'three',
        '@iconify/vue'
      ]
    }
  },

  // Content module optimizations
  content: {

    // Syntax highlighting
    highlight: {
      theme: 'github-dark',
      // Preload only essential languages
      preload: ['javascript', 'typescript', 'vue', 'css', 'html', 'json']
    },

    // Optimized markdown processing
    markdown: {
      remarkPlugins: ['remark-emoji'],
      // Lazy load heavy markdown features
      mdc: true,

      // Custom components for markdown
      tags: {
        p: 'MDParagraph',
        a: 'MDLink',
        img: 'MDImage',
        h1: 'MDH1',
        h2: 'MDH2',
        h3: 'MDH3',
        h4: 'MDH4',
        h5: 'MDH5',
        h6: 'MDH6',
        ul: 'MDList',
        ol: 'MDOrderedList',
        li: 'MDListItem',
        youtube: 'Youtube'
      },

      // Component registration
      components: {
        global: true,
        dirs: ['~/components', '~/components/content']
      }
    },

    // Content preview
    preview: {
      api: 'https://api.nuxt.studio'
    },

    // Enable content caching
    sources: {
      content: {
        driver: 'fs',
        prefix: '/blog',
        base: './content'
      }
    }
  },
  app: {
    pageTransition: {
      name: 'page',
      mode: 'out-in'
    },
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: 'Fadi Nahhas',
      meta: [
        { name: 'description', content: 'Software Engineer specializing in game development and web applications. Building creative digital experiences with modern technologies.' },
        { name: 'format-detection', content: 'telephone=no' },
        { name: 'theme-color', content: '#121212' },

        // Open Graph / Facebook
        { property: 'og:type', content: 'website' },
        { property: 'og:title', content: 'Fadi Nahhas' },
        { property: 'og:description', content: 'Software Engineer specializing in game development and web applications. Building creative digital experiences with modern technologies.' },
        { property: 'og:image', content: '/images/og-image.jpg' },

        // Twitter
        { name: 'twitter:card', content: 'summary_large_image' },
        { name: 'twitter:title', content: 'Fadi Nahhas' },
        { name: 'twitter:description', content: 'Software Engineer specializing in game development and web applications. Building creative digital experiences with modern technologies.' },
        { name: 'twitter:image', content: '/images/og-image.jpg' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
      ]
    }
  },
  experimental: {
    inlineRouteRules: false
  },
  // Auto-import components
  components: [
    { path: '~/components', pathPrefix: false },
    { path: '~/components/ui', pathPrefix: false },
    { path: '~/components/projects', pathPrefix: false },
    { path: '~/components/contact', pathPrefix: false }
  ],

  // Runtime configuration for environment variables
  runtimeConfig: {
    public: {
      siteUrl: process.env.SITE_URL || 'https://fadinahhas.com'
    }
  },

  // Performance optimizations
  nitro: {
    // Enable compression
    compressPublicAssets: true,

    // Minify output
    minify: true,

    // Enable experimental features for better performance
    experimental: {
      wasm: true
    }
  },

  // Build optimizations
  build: {
    // Analyze bundle in development
    analyze: process.env.NODE_ENV === 'development'
  },

  // SEO optimizations
  sitemap: {
    hostname: process.env.SITE_URL || 'https://fadinahhas.com',
    gzip: true,
    exclude: [
      '/admin/**'
    ],
    defaults: {
      changefreq: 'weekly',
      priority: 0.8,
      lastmod: new Date()
    }
  },

  robots: {
    UserAgent: '*',
    Allow: '/',
    Sitemap: (process.env.SITE_URL || 'https://fadinahhas.com') + '/sitemap.xml'
  }
})
