<template>
  <div>
    <!-- Particles background at the root level -->
    <ParticlesBackground />

    <div class="min-h-screen flex flex-col transition-colors duration-500 relative z-0">
      <Navigation />
      <main class="flex-grow">
        <NuxtPage />
      </main>
    </div>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { watch, onMounted } from 'vue';
import { useNuxtApp } from '#app';

const nuxtApp = useNuxtApp();
const route = useRoute();

// Handle body class only in client-side
const updateBodyClass = (path) => {
  if (import.meta.client && document?.body) {
    if (path === '/') {
      // Home page - prevent scrolling
      document.body.classList.add('no-scroll-home');
    } else {
      // Other pages - allow scrolling
      document.body.classList.remove('no-scroll-home');
    }
  }
};

// Helper function to force scroll to top for project pages
const forceScrollToTopForProjects = (path) => {
  if (!import.meta.client) return;

  // Only for project detail pages
  if (path.startsWith('/projects/') && path !== '/projects/') {
    try {
      // Reset scroll position
      window.scrollTo(0, 0);

      // Safely access DOM elements
      if (document.documentElement) {
        document.documentElement.scrollTop = 0;
      }

      if (document.body) {
        document.body.scrollTop = 0;
      }
    } catch (error) {
      console.warn('Error scrolling to top:', error);
    }
  }
};

// Watch for route changes
watch(() => route.path, (newPath, oldPath) => {
  // Update body class
  updateBodyClass(newPath);

  // Force scroll to top for project pages
  forceScrollToTopForProjects(newPath);

  // Check if we're navigating away from a project page
  const isLeavingProjectPage = oldPath &&
    oldPath.startsWith('/projects/') &&
    oldPath !== '/projects/' &&
    oldPath !== '/projects' &&
    (!newPath.startsWith('/projects/') || newPath === '/projects/' || newPath === '/projects');

  if (isLeavingProjectPage && nuxtApp.$resetTheme) {
    // Reset theme when leaving project pages using the plugin helper
    nuxtApp.$resetTheme();
  }

  // Ensure theme is correct using our plugin helper
  if (nuxtApp.$ensureTheme) {
    nuxtApp.$ensureTheme(newPath);
  }

  // Clean up any ripple elements that might be stuck during navigation
  cleanupRippleElements();
}, { immediate: false });

// Helper function to clean up ripple elements
const cleanupRippleElements = () => {
  if (!import.meta.client) return;

  try {
    // Find all ripple elements in the DOM
    const ripples = document.querySelectorAll('.ripple');
    if (ripples.length > 0) {
      ripples.forEach(ripple => {
        if (ripple && ripple.parentNode) {
          ripple.parentNode.removeChild(ripple);
        }
      });
    }
  } catch (error) {
    console.warn('Error cleaning up ripples during navigation:', error);
  }
};

// Initialize on mount
onMounted(() => {
  // Update body class based on route
  updateBodyClass(route.path);

  // Force scroll to top for project pages
  forceScrollToTopForProjects(route.path);

  // Ensure theme is correct using our plugin helper
  if (nuxtApp.$ensureTheme) {
    nuxtApp.$ensureTheme(route.path);
  }

  // Clean up any ripple elements that might be stuck from previous sessions
  cleanupRippleElements();
});
</script>

<!-- Styles moved to assets/css/tailwind.css for better SEO and performance -->