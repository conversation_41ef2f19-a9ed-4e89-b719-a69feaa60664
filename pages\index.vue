<template>
  <div class="fixed inset-0 flex items-center justify-center bg-transparent">
    <!-- SEO Component -->
    <SEO
      title="Portfolio"
      description="Software Engineer building video games & websites"
      url="/"
    />

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div
        v-motion
        :initial="{ opacity: 0, y: 100 }"
        :enter="{ opacity: 1, y: 0 }"
        class="text-center"
      >
        <h1 ref="mainTitle" class="text-5xl md:text-7xl font-bold mb-4 md:mb-6 leading-tight relative">
          <span class="main-text bg-clip-text text-transparent" :class="titleGradientClass">Fadi <PERSON></span>
          <span ref="glowText" class="glow-text absolute top-0 left-0 w-full h-full">Fadi Nahhas</span>
        </h1>
        <p ref="subtitle" class="text-xl md:text-2xl text-gray-300 mb-8 md:mb-10 font-light tracking-wider leading-relaxed">
          Software Engineer building video games & websites
        </p>

        <!-- Clean, Purposeful Button -->
        <div class="inline-block">
          <NuxtLink
            to="/projects"
            id="projects-button"
            class="group relative inline-flex items-center justify-center px-6 py-3 md:px-8 md:py-4 overflow-hidden rounded-md text-white font-medium text-sm md:text-base"
            :class="buttonClass"
          >
            <span class="relative z-10 flex items-center justify-center space-x-2">
              <span>View Projects</span>
              <Icon icon="mdi:arrow-right" class="w-4 h-4 md:w-5 md:h-5 transform transition-transform duration-500 group-hover:translate-x-1" />
            </span>
            <span class="absolute inset-0 z-0" :class="buttonGradientClass"></span>
            <span class="absolute bottom-0 left-0 h-1 transition-all duration-500 ease-out origin-left group-hover:w-full w-0" :class="buttonHighlightClass"></span>
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, computed, watch } from 'vue';
import { gsap } from 'gsap';
import { Icon } from '@iconify/vue';
import { useThemeStore } from '~/stores/theme-optimized';

// SEO is now handled by the SEO component

// Refs for title and subtitle
const mainTitle = ref(null);
const subtitle = ref(null);
const glowText = ref(null);

// Get the theme store
const themeStore = useThemeStore();

// Computed properties for theme-based classes
const titleGradientClass = computed(() => {
  return themeStore.isGameDev
    ? 'bg-gradient-to-r from-green-400 to-green-600'
    : 'bg-gradient-to-r from-blue-400 to-blue-600';
});

const buttonClass = computed(() => {
  return themeStore.isGameDev ? 'bg-primary-button-green' : 'bg-primary-button-blue';
});

const buttonGradientClass = computed(() => {
  return themeStore.isGameDev
    ? 'bg-gradient-to-r from-green-600 to-green-500'
    : 'bg-gradient-to-r from-blue-600 to-blue-500';
});

const buttonHighlightClass = computed(() => {
  return themeStore.isGameDev ? 'bg-green-400' : 'bg-blue-400';
});

// Title and subtitle animations
const initTitleAnimations = () => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || !document) return;

  // Check if elements exist
  if (!mainTitle.value || !glowText.value) return;

  // Create a timeline for the title animations
  const tl = gsap.timeline({ delay: 0.3 });

  // Determine gradient colors based on theme
  const updateTitleGradient = () => {
    const colors = themeStore.isGameDev
      ? ['#4ade80', '#16a34a', '#4ade80'] // green-400, green-600, green-400
      : ['#60a5fa', '#2563eb', '#60a5fa']; // blue-400, blue-600, blue-400

    // Update the title gradient
    gsap.to(mainTitle.value, {
      backgroundImage: `linear-gradient(to right, ${colors[0]}, ${colors[1]}, ${colors[0]})`,
      duration: 0.5
    });
  };

  // Initial gradient setup
  updateTitleGradient();

  // Watch for theme changes to update the gradient
  watch(() => themeStore.isGameDev, () => {
    updateTitleGradient();
  });

  // Animate the gradient position
  tl.fromTo(mainTitle.value,
    {
      backgroundSize: '200% 100%',
      backgroundPosition: '0% 0%'
    },
    {
      backgroundPosition: '100% 0%',
      duration: 3,
      ease: 'power1.inOut',
      repeat: -1,
      yoyo: true
    }
  );

  // Create a timeline for the sweeping glow that goes back and forth
  const glowTimeline = gsap.timeline({
    repeat: -1,
    repeatDelay: 2
  });

  // Animate the glow text to sweep from left to right
  glowTimeline.fromTo(glowText.value,
    { backgroundPosition: '-100% 0' },
    {
      backgroundPosition: '200% 0',
      duration: 4, // Slower animation
      ease: 'power1.inOut'
    }
  )
  // Pause before going back
  .to(glowText.value, {
    backgroundPosition: '200% 0',
    duration: 1
  })
  // Animate the glow text to sweep from right to left
  .fromTo(glowText.value,
    { backgroundPosition: '200% 0' },
    {
      backgroundPosition: '-100% 0',
      duration: 4, // Slower animation
      ease: 'power1.inOut'
    }
  )
  // Pause before starting again
  .to(glowText.value, {
    backgroundPosition: '-100% 0',
    duration: 1
  });

  // Add a subtle shimmer effect to the main text gradient
  gsap.to('.main-text', {
    backgroundPosition: '200% 0',
    duration: 4,
    ease: 'linear',
    repeat: -1
  });
};

// Simple button animation
const initButtonAnimation = () => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || !document) return;

  const button = document.getElementById('projects-button');
  if (!button) return;

  // Function to update button shadow color based on theme
  const updateButtonShadow = () => {
    const shadowColor = themeStore.isGameDev
      ? 'rgba(0, 50, 0, 0.3)' // Green shadow
      : 'rgba(0, 30, 60, 0.3)'; // Blue shadow

    const baseShadowColor = themeStore.isGameDev
      ? 'rgba(0, 50, 0, 0.2)' // Green base shadow
      : 'rgba(0, 30, 60, 0.2)'; // Blue base shadow

    // Update button shadow
    gsap.to(button, {
      boxShadow: `0 4px 6px ${baseShadowColor}`,
      duration: 0.3
    });

    // Store shadow colors for hover effect
    button.dataset.hoverShadow = `0 6px 16px ${shadowColor}`;
    button.dataset.baseShadow = `0 4px 6px ${baseShadowColor}`;
  };

  // Initial shadow setup
  updateButtonShadow();

  // Watch for theme changes to update button shadow
  watch(() => themeStore.isGameDev, () => {
    updateButtonShadow();
  });

  // Create a subtle hover effect
  button.addEventListener('mouseenter', () => {
    gsap.to(button, {
      y: -4,
      boxShadow: button.dataset.hoverShadow,
      duration: 0.3,
      ease: 'power2.out'
    });
  });

  button.addEventListener('mouseleave', () => {
    gsap.to(button, {
      y: 0,
      boxShadow: button.dataset.baseShadow,
      duration: 0.3,
      ease: 'power2.out'
    });
  });

  // Click effect
  button.addEventListener('mousedown', () => {
    gsap.to(button, {
      scale: 0.97,
      duration: 0.1,
      ease: 'power2.in'
    });
  });

  button.addEventListener('mouseup', () => {
    gsap.to(button, {
      scale: 1,
      duration: 0.2,
      ease: 'power2.out'
    });
  });
};

// Clean up function
const cleanupAnimation = () => {
  const button = document.getElementById('projects-button');
  if (!button) return;

  button.removeEventListener('mouseenter', () => {});
  button.removeEventListener('mouseleave', () => {});
  button.removeEventListener('mousedown', () => {});
  button.removeEventListener('mouseup', () => {});
};

// Initialize on mount
onMounted(() => {
  initTitleAnimations();
  initButtonAnimation();
});

// Clean up on unmount
onUnmounted(() => {
  cleanupAnimation();
});
</script>

<style scoped>
.bg-primary-button-green {
  background-color: var(--primary-color);
}

.bg-primary-button-blue {
  background-color: var(--primary-color);
}

#projects-button {
  transition: transform 0.3s, box-shadow 0.3s;
  letter-spacing: 0.5px;
}

/* Title and subtitle styles */
h1 {
  -webkit-background-clip: text;
  background-clip: text;
  letter-spacing: -0.02em;
  will-change: text-shadow, background-position;
}

.main-text {
  -webkit-background-clip: text;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.glow-text {
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  z-index: 2;
  pointer-events: none;
  background-image: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0) 40%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0) 60%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  background-position: -100% 0;
}

p {
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

/* Custom styles for index page */
</style>