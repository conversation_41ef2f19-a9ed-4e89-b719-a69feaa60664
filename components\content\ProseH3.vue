<template>
  <h3 class="heading text-xl font-semibold mt-8 mb-3 pl-2 opacity-90" :style="{ color: 'var(--primary-color)' }">
    <div class="heading-content">
      <slot />
    </div>
    <div class="heading-decoration" :style="{
      backgroundColor: `rgba(var(--primary-rgb), 0.15)`,
      borderLeftColor: 'var(--primary-color)',
      boxShadow: `0 4px 6px -1px rgba(var(--primary-rgb), 0.3)`
    }"></div>
  </h3>
</template>

<script setup>
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
});
</script>

<style scoped>
.heading {
  position: relative;
}

.heading-decoration {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  border-radius: 0.25rem;
  border-left-width: 4px;
  z-index: -1;
}

.heading-content {
  position: relative;
  z-index: 1;
}
</style>
