<template>
  <div class="gallery-component my-6">
    <!-- Empty state message -->
    <div v-if="carouselItems.length === 0" class="bg-gray-800 bg-opacity-20 rounded-lg p-4 text-center text-gray-400">
      No gallery images available
    </div>

    <!-- Carousel container -->
    <div v-else class="carousel-container relative overflow-hidden rounded-lg shadow-lg" style="height: 300px;">
      <!-- Slides -->
      <div
        class="carousel-slides flex transition-transform duration-500 ease-out h-full"
        :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
      >
        <div v-for="(item, index) in carouselItems" :key="index" class="carousel-slide flex-shrink-0 w-full h-full relative">
          <img
            :src="item.src"
            :alt="item.alt"
            class="w-full h-full object-contain cursor-pointer"
            @click="openFullscreen(index)"
          />
        </div>
      </div>

      <!-- Navigation arrows -->
      <button
        v-if="carouselItems.length > 1"
        @click.stop="prevSlide"
        class="absolute left-2 top-1/2 transform -translate-y-1/2 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all duration-300"
        :style="{ color: primaryColor }"
      >
        <IconifyIcon icon="mdi:chevron-left" class="w-5 h-5" />
      </button>

      <button
        v-if="carouselItems.length > 1"
        @click.stop="nextSlide"
        class="absolute right-2 top-1/2 transform -translate-y-1/2 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all duration-300"
        :style="{ color: primaryColor }"
      >
        <IconifyIcon icon="mdi:chevron-right" class="w-5 h-5" />
      </button>

      <!-- Fullscreen button -->
      <button
        @click.stop="openFullscreen(currentIndex)"
        class="absolute right-2 top-2 z-10 p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all duration-300"
        :style="{ color: primaryColor }"
      >
        <IconifyIcon icon="mdi:fullscreen" class="w-5 h-5" />
      </button>

      <!-- Dots indicator -->
      <div
        v-if="carouselItems.length > 1"
        class="absolute bottom-2 left-0 right-0 flex justify-center gap-1 z-10"
      >
        <button
          v-for="(_, index) in carouselItems"
          :key="index"
          @click.stop="goToSlide(index)"
          class="w-2 h-2 rounded-full transition-all duration-300"
          :class="{ 'scale-125': currentIndex === index }"
          :style="{
            backgroundColor: currentIndex === index ? primaryColor : 'rgba(255, 255, 255, 0.5)',
            boxShadow: currentIndex === index ? `0 0 3px ${primaryColor}` : 'none'
          }"
        ></button>
      </div>
    </div>

    <!-- Fullscreen Gallery -->
    <FullscreenGallery
      :is-open="isFullscreenOpen"
      :items="carouselItems"
      :initial-index="fullscreenIndex"
      @close="closeFullscreen"
      @change="updateFullscreenIndex"
    />

    <slot />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useThemeStore } from '~/stores/theme-optimized';
import { gsap } from 'gsap';
import FullscreenGallery from '~/components/ui/FullscreenGallery.vue';

// State
const currentIndex = ref(0);
const themeStore = useThemeStore();
const isFullscreenOpen = ref(false);
const fullscreenIndex = ref(0);

// Props to receive items and alt text from markdown
const props = defineProps({
  items: {
    type: String,
    default: ''
  },
  alt: {
    type: String,
    default: ''
  }
});

// Parse items and alt text from props
const itemsArray = props.items ? props.items.split(',').map(item => item.trim()) : [];
const altArray = props.alt ? props.alt.split(',').map(alt => alt.trim()) : [];

// Create carousel items dynamically
const carouselItems = itemsArray.map((item, index) => {
  // Check if item is a video by extension
  const isVideo = item.endsWith('.mp4') || item.endsWith('.webm') || item.endsWith('.mov');
  return {
    type: isVideo ? 'video' : 'image',
    src: item,
    alt: altArray[index] || `Gallery image ${index + 1}`
  };
});

// Computed properties
const primaryColor = computed(() => {
  return themeStore.themeColors.primary;
});

// Methods
const nextSlide = () => {
  gsap.to('.carousel-slides', {
    opacity: 0.8,
    duration: 0.2,
    onComplete: () => {
      currentIndex.value = (currentIndex.value + 1) % carouselItems.length;
      gsap.to('.carousel-slides', {
        opacity: 1,
        duration: 0.3
      });
    }
  });
};

const prevSlide = () => {
  gsap.to('.carousel-slides', {
    opacity: 0.8,
    duration: 0.2,
    onComplete: () => {
      currentIndex.value = (currentIndex.value - 1 + carouselItems.length) % carouselItems.length;
      gsap.to('.carousel-slides', {
        opacity: 1,
        duration: 0.3
      });
    }
  });
};

const goToSlide = (index) => {
  if (index === currentIndex.value) return;

  gsap.to('.carousel-slides', {
    opacity: 0.8,
    duration: 0.2,
    onComplete: () => {
      currentIndex.value = index;
      gsap.to('.carousel-slides', {
        opacity: 1,
        duration: 0.3
      });
    }
  });
};

// Fullscreen gallery methods
const openFullscreen = (index) => {
  fullscreenIndex.value = index;
  isFullscreenOpen.value = true;
};

const closeFullscreen = () => {
  isFullscreenOpen.value = false;
};

const updateFullscreenIndex = (index) => {
  fullscreenIndex.value = index;
};
</script>

<style scoped>
.carousel-container {
  background-color: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.carousel-slide {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.1);
}

.carousel-slide img {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.carousel-slide img:hover {
  transform: scale(1.02);
}

/* No lightGallery styles needed */
</style>
