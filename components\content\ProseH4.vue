<template>
  <h4 class="heading text-lg font-medium mt-6 mb-2 opacity-85" :style="{ color: 'var(--primary-color)' }">
    <div class="heading-content">
      <slot />
    </div>
  </h4>
</template>

<script setup>
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
});
</script>

<style scoped>
.heading {
  position: relative;
}

.heading-content {
  position: relative;
  z-index: 1;
}
</style>
